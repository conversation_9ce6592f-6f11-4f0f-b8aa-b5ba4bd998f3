/**
 * 增强版记录列表页面 - 包含搜索、筛选、分页等功能
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

interface Record {
  id: number;
  name: string;
  amount: number;
  type: 'income' | 'expense';
  description: string;
  category: string;
  createdAt: string;
}

interface AccountBook {
  id: number;
  name: string;
  description: string;
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

interface Filters {
  search: string;
  type: string;
  sortBy: string;
  sortOrder: string;
  startDate: string;
  endDate: string;
}

const EnhancedRecordListPage: React.FC = () => {
  const navigate = useNavigate();
  const { bookId } = useParams<{ bookId: string }>();
  const accountBookId = parseInt(bookId || '0');

  const [accountBook, setAccountBook] = useState<AccountBook | null>(null);
  const [records, setRecords] = useState<Record[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });
  const [filters, setFilters] = useState<Filters>({
    search: '',
    type: '',
    sortBy: 'date',
    sortOrder: 'desc',
    startDate: '',
    endDate: '',
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载账本信息
  useEffect(() => {
    const fetchAccountBook = async () => {
      try {
        const response = await fetch(`http://localhost:3001/api/account-books/${accountBookId}`);
        const data = await response.json();
        if (data.success) {
          setAccountBook(data.data);
        }
      } catch (err) {
        console.error('获取账本信息失败:', err);
      }
    };

    if (accountBookId) {
      fetchAccountBook();
    }
  }, [accountBookId]);

  // 加载记录列表
  const fetchRecords = async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        search: filters.search,
        type: filters.type,
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,
        startDate: filters.startDate,
        endDate: filters.endDate,
      });

      const response = await fetch(`http://localhost:3001/api/records/${accountBookId}?${params}`);
      const data = await response.json();

      if (data.success) {
        setRecords(data.data.records);
        setPagination(data.data.pagination);
      } else {
        setError(data.message || '获取记录列表失败');
      }
    } catch (err) {
      setError('网络错误');
      console.error('获取记录列表失败:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (accountBookId) {
      fetchRecords();
    }
  }, [accountBookId, pagination.page, pagination.limit, filters]);

  const handleFilterChange = (key: keyof Filters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
    setPagination(prev => ({ ...prev, page: 1 })); // 重置到第一页
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const handleDeleteRecord = async (recordId: number) => {
    if (!confirm('确定要删除这条记录吗？')) {
      return;
    }

    try {
      const response = await fetch(`http://localhost:3001/api/records/${accountBookId}/${recordId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        alert('记录删除成功！');
        fetchRecords(); // 重新加载记录列表
      } else {
        alert(data.message || '删除记录失败');
      }
    } catch (err) {
      alert('网络错误，请重试');
      console.error('删除记录失败:', err);
    }
  };

  const handleBack = () => {
    navigate('/dashboard');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  if (loading && records.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button 
                onClick={handleBack}
                className="mr-4 px-3 py-1 text-gray-600 hover:text-gray-800"
              >
                ← 返回
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {accountBook?.name || '账本记录'}
                </h1>
                {accountBook?.description && (
                  <p className="text-gray-600">{accountBook.description}</p>
                )}
              </div>
            </div>
            <button 
              onClick={() => navigate(`/account-books/${accountBookId}/records/new`)}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              添加记录
            </button>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 筛选和搜索 */}
          <div className="bg-white shadow rounded-lg mb-6">
            <div className="px-4 py-5 sm:p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* 搜索 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    搜索
                  </label>
                  <input
                    type="text"
                    value={filters.search}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    placeholder="搜索记录名称或描述"
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* 类型筛选 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    类型
                  </label>
                  <select
                    value={filters.type}
                    onChange={(e) => handleFilterChange('type', e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">全部</option>
                    <option value="income">收入</option>
                    <option value="expense">支出</option>
                  </select>
                </div>

                {/* 排序 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    排序
                  </label>
                  <select
                    value={`${filters.sortBy}-${filters.sortOrder}`}
                    onChange={(e) => {
                      const [sortBy, sortOrder] = e.target.value.split('-');
                      handleFilterChange('sortBy', sortBy);
                      handleFilterChange('sortOrder', sortOrder);
                    }}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="date-desc">时间（新到旧）</option>
                    <option value="date-asc">时间（旧到新）</option>
                    <option value="amount-desc">金额（高到低）</option>
                    <option value="amount-asc">金额（低到高）</option>
                    <option value="name-asc">名称（A-Z）</option>
                    <option value="name-desc">名称（Z-A）</option>
                  </select>
                </div>

                {/* 每页显示数量 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    每页显示
                  </label>
                  <select
                    value={pagination.limit}
                    onChange={(e) => setPagination(prev => ({ ...prev, limit: parseInt(e.target.value), page: 1 }))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value={5}>5条</option>
                    <option value={10}>10条</option>
                    <option value={20}>20条</option>
                    <option value={50}>50条</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* 记录列表 */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-medium text-gray-900">
                  记录列表 ({pagination.total} 条)
                </h2>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
                  <p className="text-red-700">{error}</p>
                </div>
              )}

              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-600">加载中...</p>
                </div>
              ) : records.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-6xl mb-4">📝</div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">还没有记录</h3>
                  <p className="text-gray-600 mb-4">添加您的第一条记录，开始记账</p>
                  <button 
                    onClick={() => navigate(`/account-books/${accountBookId}/records/new`)}
                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    添加第一条记录
                  </button>
                </div>
              ) : (
                <>
                  {/* 记录列表 */}
                  <div className="space-y-4">
                    {records.map((record) => (
                      <div key={record.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                record.type === 'income' 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-red-100 text-red-800'
                              }`}>
                                {record.type === 'income' ? '收入' : '支出'}
                              </span>
                              <span className="font-medium text-gray-900">{record.name}</span>
                            </div>
                            {record.description && (
                              <p className="text-gray-600 text-sm mb-1">{record.description}</p>
                            )}
                            <p className="text-gray-500 text-xs">{formatDate(record.createdAt)}</p>
                          </div>
                          <div className="text-right">
                            <div className="flex items-center justify-end space-x-3">
                              <span className={`text-lg font-semibold ${
                                record.type === 'income' ? 'text-green-600' : 'text-red-600'
                              }`}>
                                {record.type === 'income' ? '+' : '-'}¥{record.amount.toFixed(2)}
                              </span>
                              <div className="flex space-x-2">
                                <button
                                  onClick={() => navigate(`/account-books/${accountBookId}/records/${record.id}/edit`)}
                                  className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                                >
                                  编辑
                                </button>
                                <button
                                  onClick={() => handleDeleteRecord(record.id)}
                                  className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200"
                                >
                                  删除
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* 分页 */}
                  {pagination.totalPages > 1 && (
                    <div className="mt-6 flex items-center justify-between">
                      <div className="text-sm text-gray-700">
                        显示第 {(pagination.page - 1) * pagination.limit + 1} - {Math.min(pagination.page * pagination.limit, pagination.total)} 条，
                        共 {pagination.total} 条记录
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handlePageChange(pagination.page - 1)}
                          disabled={pagination.page <= 1}
                          className={`px-3 py-1 rounded ${
                            pagination.page <= 1
                              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                              : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          上一页
                        </button>
                        
                        {/* 页码 */}
                        {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                          const pageNum = Math.max(1, pagination.page - 2) + i;
                          if (pageNum > pagination.totalPages) return null;
                          
                          return (
                            <button
                              key={pageNum}
                              onClick={() => handlePageChange(pageNum)}
                              className={`px-3 py-1 rounded ${
                                pageNum === pagination.page
                                  ? 'bg-blue-600 text-white'
                                  : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
                              }`}
                            >
                              {pageNum}
                            </button>
                          );
                        })}

                        <button
                          onClick={() => handlePageChange(pagination.page + 1)}
                          disabled={pagination.page >= pagination.totalPages}
                          className={`px-3 py-1 rounded ${
                            pagination.page >= pagination.totalPages
                              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                              : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          下一页
                        </button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default EnhancedRecordListPage;
