/**
 * 简化版月份跨越处理API
 * 临时版本，用于解决导入错误
 */

import { apiClient } from './client';

export interface MonthCrossingResult {
  fromMonth: string;
  toMonth: string;
  migratedCount: number;
  resetCount: number;
  success: boolean;
  message: string;
}

export interface MonthCrossingHistory {
  month: string;
  recordCount: number;
  completedAt: string;
}

export interface MonthCrossingStatus {
  currentMonth: string;
  needsCrossing: boolean;
  crossingResult: MonthCrossingResult | null;
}

/**
 * 手动处理月份跨越 - 简化版本
 */
export const handleMonthCrossing = async (
  accountBookId: number,
  fromMonth: string,
  toMonth: string
): Promise<MonthCrossingResult> => {
  try {
    const response = await apiClient.post(
      `/account-books/${accountBookId}/month-crossing`,
      { fromMonth, toMonth }
    );

    return {
      fromMonth,
      toMonth,
      migratedCount: response.data?.migratedCount || 0,
      resetCount: response.data?.resetCount || 0,
      success: true,
      message: response.data?.message || '月份跨越处理成功',
    };
  } catch (error) {
    console.error('月份跨越处理失败:', error);
    return {
      fromMonth,
      toMonth,
      migratedCount: 0,
      resetCount: 0,
      success: false,
      message: '月份跨越处理失败',
    };
  }
};

/**
 * 自动处理月份跨越 - 简化版本
 */
export const autoHandleMonthCrossing = async (
  accountBookId: number
): Promise<MonthCrossingResult | null> => {
  try {
    const response = await apiClient.post(
      `/account-books/${accountBookId}/month-crossing/auto`
    );

    if (response.data) {
      return {
        fromMonth: response.data.fromMonth || '',
        toMonth: response.data.toMonth || '',
        migratedCount: response.data.migratedCount || 0,
        resetCount: response.data.resetCount || 0,
        success: true,
        message: response.data.message || '自动月份跨越处理成功',
      };
    }

    return null; // 无需处理
  } catch (error) {
    console.error('自动月份跨越处理失败:', error);
    return null;
  }
};

/**
 * 获取月份跨越历史 - 简化版本
 */
export const getMonthCrossingHistory = async (
  accountBookId: number,
  limit: number = 10
): Promise<MonthCrossingHistory[]> => {
  try {
    const response = await apiClient.get(
      `/account-books/${accountBookId}/month-crossing/history?limit=${limit}`
    );

    return response.data?.history || [];
  } catch (error) {
    console.error('获取月份跨越历史失败:', error);
    return [];
  }
};

/**
 * 获取月份跨越状态 - 简化版本
 */
export const getMonthCrossingStatus = async (
  accountBookId: number
): Promise<MonthCrossingStatus> => {
  try {
    const response = await apiClient.get(
      `/account-books/${accountBookId}/month-crossing/status`
    );

    return {
      currentMonth: response.data?.currentMonth || new Date().toISOString().slice(0, 7),
      needsCrossing: response.data?.needsCrossing || false,
      crossingResult: response.data?.crossingResult || null,
    };
  } catch (error) {
    console.error('获取月份跨越状态失败:', error);
    return {
      currentMonth: new Date().toISOString().slice(0, 7),
      needsCrossing: false,
      crossingResult: null,
    };
  }
};

/**
 * 简化版月份跨越API对象
 */
export const monthCrossingApi = {
  /**
   * 手动处理月份跨越
   */
  handleCrossing: handleMonthCrossing,

  /**
   * 自动处理月份跨越
   */
  autoCrossing: autoHandleMonthCrossing,

  /**
   * 获取月份跨越历史
   */
  getHistory: getMonthCrossingHistory,

  /**
   * 获取月份跨越状态
   */
  getStatus: getMonthCrossingStatus,
};

export default monthCrossingApi;
