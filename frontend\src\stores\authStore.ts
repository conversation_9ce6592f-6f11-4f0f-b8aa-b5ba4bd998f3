/**
 * 用户认证状态管理
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { loginApi, registerApi, getCurrentUserApi } from '../api/authApi';
import { apiClient } from '../api/client';

// 临时内联类型定义
interface User {
  id: number;
  username: string;
  email: string;
  createdAt: string;
  updatedAt: string;
}

interface AuthResponse {
  token: string;
  user: User;
}

interface LoginForm {
  username: string;
  password: string;
}

interface RegisterForm {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

interface AuthState {
  // 状态
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;

  // 操作
  login: (credentials: LoginForm) => Promise<void>;
  register: (userData: RegisterForm) => Promise<void>;
  logout: () => void;
  clearError: () => void;
  checkAuth: () => Promise<void>;
  updateUser: (user: User) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      user: null,
      token: null,
      isAuthenticated: false,
      loading: false,
      error: null,

      // 登录
      login: async (credentials: LoginForm) => {
        set({ loading: true, error: null });

        try {
          const response = await loginApi(credentials);

          // 设置token到API客户端
          apiClient.setToken(response.token);

          set({
            user: response.user,
            token: response.token,
            isAuthenticated: true,
            loading: false,
            error: null,
          });
        } catch (error) {
          const message = error instanceof Error ? error.message : '登录失败';
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            loading: false,
            error: message,
          });
          throw error;
        }
      },

      // 注册
      register: async (userData: RegisterForm) => {
        set({ loading: true, error: null });

        try {
          const response = await registerApi({
            username: userData.username,
            email: userData.email,
            password: userData.password,
          });

          // 设置token到API客户端
          apiClient.setToken(response.token);

          set({
            user: response.user,
            token: response.token,
            isAuthenticated: true,
            loading: false,
            error: null,
          });
        } catch (error) {
          const message = error instanceof Error ? error.message : '注册失败';
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            loading: false,
            error: message,
          });
          throw error;
        }
      },

      // 登出
      logout: () => {
        // 清除API客户端token
        apiClient.clearToken();

        set({
          user: null,
          token: null,
          isAuthenticated: false,
          loading: false,
          error: null,
        });
      },

      // 清除错误
      clearError: () => {
        set({ error: null });
      },

      // 更新用户信息
      updateUser: (user: User) => {
        set({ user });
      },

      // 检查认证状态
      checkAuth: async () => {
        const { token } = get();
        
        if (!token) {
          return;
        }

        set({ loading: true });

        try {
          // 验证token有效性
          const user = await getCurrentUserApi();

          set({
            user,
            isAuthenticated: true,
            loading: false,
            error: null,
          });
        } catch (error) {
          // token无效，清除认证状态
          apiClient.clearToken();
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            loading: false,
            error: null,
          });
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        // 恢复状态后设置token到API客户端
        if (state?.token) {
          apiClient.setToken(state.token);
        }
      },
    }
  )
);
