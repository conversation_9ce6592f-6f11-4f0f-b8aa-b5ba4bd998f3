/**
 * 账本创建/编辑页面
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAccountBookStore } from '../stores/accountBookStore';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/Card';
import { Alert, AlertDescription } from '../components/ui/Alert';

// 临时内联类型定义
interface AccountBookForm {
  name: string;
  description: string;
}

const AccountBookFormPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEdit = id !== 'new' && id !== undefined;
  const bookId = isEdit ? parseInt(id || '0') : 0;

  const { 
    currentBook, 
    loading, 
    error, 
    fetchBookById, 
    createBook, 
    updateBook, 
    clearError 
  } = useAccountBookStore();

  const [formData, setFormData] = useState<AccountBookForm>({
    name: '',
    description: '',
  });

  const [formErrors, setFormErrors] = useState<Partial<AccountBookForm>>({});

  // 加载账本数据（编辑模式）
  useEffect(() => {
    if (isEdit && bookId) {
      fetchBookById(bookId);
    }
  }, [isEdit, bookId, fetchBookById]);

  // 填充表单数据（编辑模式）
  useEffect(() => {
    if (isEdit && currentBook && currentBook.id === bookId) {
      setFormData({
        name: currentBook.name,
        description: currentBook.description || '',
      });
    }
  }, [isEdit, currentBook, bookId]);

  // 表单验证
  const validateForm = (): boolean => {
    const errors: Partial<AccountBookForm> = {};

    if (!formData.name.trim()) {
      errors.name = '请输入账本名称';
    } else if (formData.name.trim().length > 100) {
      errors.name = '账本名称不能超过100个字符';
    }

    if (formData.description && formData.description.length > 500) {
      errors.description = '描述不能超过500个字符';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // 清除对应字段的错误
    if (formErrors[name as keyof AccountBookForm]) {
      setFormErrors(prev => ({ ...prev, [name]: undefined }));
    }
    
    // 清除全局错误
    if (error) {
      clearError();
    }
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      if (isEdit) {
        await updateBook(bookId, formData);
      } else {
        await createBook(formData);
      }
      navigate('/dashboard');
    } catch (err) {
      console.error('Save book failed:', err);
    }
  };

  // 处理取消
  const handleCancel = () => {
    navigate('/dashboard');
  };

  if (isEdit && loading && !currentBook) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航栏 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Button variant="ghost" onClick={handleCancel} className="mr-4">
                ← 返回
              </Button>
              <h1 className="text-xl font-semibold text-gray-900">
                {isEdit ? '编辑账本' : '创建账本'}
              </h1>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-2xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <Card>
            <CardHeader>
              <CardTitle>{isEdit ? '编辑账本' : '创建新账本'}</CardTitle>
              <CardDescription>
                {isEdit 
                  ? '修改账本的基本信息' 
                  : '填写账本信息，创建一个新的记账账本'
                }
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <Input
                  label="账本名称"
                  name="name"
                  type="text"
                  value={formData.name}
                  onChange={handleInputChange}
                  error={formErrors.name}
                  placeholder="请输入账本名称"
                  helperText="账本名称不能超过100个字符"
                  required
                />

                <div className="space-y-2">
                  <label className="text-sm font-medium leading-none">
                    描述（可选）
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="请输入账本描述"
                    rows={4}
                    className="flex w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  />
                  {formErrors.description && (
                    <p className="text-sm text-red-600">{formErrors.description}</p>
                  )}
                  <p className="text-sm text-gray-500">
                    描述不能超过500个字符（当前：{formData.description.length}/500）
                  </p>
                </div>

                <div className="flex space-x-4 pt-4">
                  <Button
                    type="submit"
                    loading={loading}
                    disabled={loading}
                    className="flex-1"
                  >
                    {loading 
                      ? (isEdit ? '保存中...' : '创建中...') 
                      : (isEdit ? '保存更改' : '创建账本')
                    }
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancel}
                    disabled={loading}
                    className="flex-1"
                  >
                    取消
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default AccountBookFormPage;
