/**
 * 安全设置组件
 */

import React, { useState } from 'react';
import { useUserSettingsStore } from '../../stores/userSettingsStore';
import { UserSettings } from '../../api/userSettingsApi';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Button,
  Input,
  Label,
  Alert,
  AlertDescription,
  Modal
} from '../ui';

interface SecuritySectionProps {
  settings: UserSettings | null;
  loading: boolean;
}

const SecuritySection: React.FC<SecuritySectionProps> = ({
  settings,
  loading,
}) => {
  const {
    updateUserPassword,
    loading: operationLoading,
    error
  } = useUserSettingsStore();

  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [passwordError, setPasswordError] = useState('');
  const [passwordSuccess, setPasswordSuccess] = useState('');

  // 处理密码表单变化
  const handlePasswordFormChange = (field: string, value: string) => {
    setPasswordForm(prev => ({
      ...prev,
      [field]: value,
    }));
    setPasswordError('');
    setPasswordSuccess('');
  };

  // 验证密码表单
  const validatePasswordForm = () => {
    if (!passwordForm.currentPassword) {
      setPasswordError('请输入当前密码');
      return false;
    }
    
    if (!passwordForm.newPassword) {
      setPasswordError('请输入新密码');
      return false;
    }
    
    if (passwordForm.newPassword.length < 6) {
      setPasswordError('新密码至少需要6个字符');
      return false;
    }
    
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      setPasswordError('两次输入的新密码不一致');
      return false;
    }
    
    if (passwordForm.currentPassword === passwordForm.newPassword) {
      setPasswordError('新密码不能与当前密码相同');
      return false;
    }
    
    return true;
  };

  // 更新密码
  const handleUpdatePassword = async () => {
    if (!validatePasswordForm()) return;

    try {
      await updateUserPassword({
        currentPassword: passwordForm.currentPassword,
        newPassword: passwordForm.newPassword,
      });
      
      // 重置表单
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
      setPasswordSuccess('密码更新成功！');

      // 3秒后关闭模态框
      setTimeout(() => {
        setShowPasswordModal(false);
        setPasswordSuccess('');
      }, 2000);
    } catch (error) {
      console.error('Failed to update password:', error);
    }
  };

  // 关闭密码模态框
  const handleClosePasswordModal = () => {
    setShowPasswordModal(false);
    setPasswordForm({
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    });
    setPasswordError('');
    setPasswordSuccess('');
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="space-y-4">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!settings) {
    return (
      <Alert>
        <AlertDescription>
          无法加载安全设置信息
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* 密码管理 */}
      <Card>
        <CardHeader>
          <CardTitle>密码管理</CardTitle>
          <CardDescription>更改您的登录密码</CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label>登录密码</Label>
              <p className="text-sm text-gray-600">
                最后更新: {new Date(settings.updatedAt).toLocaleDateString()}
              </p>
            </div>
            <Button
              onClick={() => setShowPasswordModal(true)}
              disabled={operationLoading}
            >
              更改密码
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 账户安全 */}
      <Card>
        <CardHeader>
          <CardTitle>账户安全</CardTitle>
          <CardDescription>保护您的账户安全</CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label>两步验证</Label>
              <p className="text-sm text-gray-600">
                为您的账户添加额外的安全保护
              </p>
            </div>
            <Button variant="outline" disabled>
              即将推出
            </Button>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label>登录历史</Label>
              <p className="text-sm text-gray-600">
                查看最近的登录记录和设备信息
              </p>
            </div>
            <Button variant="outline" disabled>
              查看历史
            </Button>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label>活跃会话</Label>
              <p className="text-sm text-gray-600">
                管理当前登录的设备和会话
              </p>
            </div>
            <Button variant="outline" disabled>
              管理会话
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 安全建议 */}
      <Card className="bg-yellow-50 border-yellow-200">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h3 className="text-sm font-medium text-yellow-800">安全建议</h3>
              <div className="mt-2 text-sm text-yellow-700">
                <ul className="list-disc list-inside space-y-1">
                  <li>使用强密码，包含字母、数字和特殊字符</li>
                  <li>定期更换密码，建议每3-6个月更换一次</li>
                  <li>不要在多个网站使用相同的密码</li>
                  <li>不要与他人分享您的登录凭据</li>
                  <li>在公共设备上使用后记得退出登录</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 更改密码模态框 */}
      <Modal
        isOpen={showPasswordModal}
        onClose={handleClosePasswordModal}
        title="更改密码"
        size="md"
      >
        <div className="space-y-4">
          {passwordError && (
            <Alert variant="destructive">
              <AlertDescription>{passwordError}</AlertDescription>
            </Alert>
          )}

          {passwordSuccess && (
            <Alert variant="default" className="border-green-200 bg-green-50">
              <AlertDescription className="text-green-800">{passwordSuccess}</AlertDescription>
            </Alert>
          )}

          <div>
            <Label htmlFor="currentPassword">当前密码</Label>
            <Input
              id="currentPassword"
              type="password"
              value={passwordForm.currentPassword}
              onChange={(e) => handlePasswordFormChange('currentPassword', e.target.value)}
              placeholder="请输入当前密码"
              disabled={operationLoading}
            />
          </div>

          <div>
            <Label htmlFor="newPassword">新密码</Label>
            <Input
              id="newPassword"
              type="password"
              value={passwordForm.newPassword}
              onChange={(e) => handlePasswordFormChange('newPassword', e.target.value)}
              placeholder="请输入新密码（至少6个字符）"
              disabled={operationLoading}
            />
          </div>

          <div>
            <Label htmlFor="confirmPassword">确认新密码</Label>
            <Input
              id="confirmPassword"
              type="password"
              value={passwordForm.confirmPassword}
              onChange={(e) => handlePasswordFormChange('confirmPassword', e.target.value)}
              placeholder="请再次输入新密码"
              disabled={operationLoading}
            />
          </div>

          <div className="flex space-x-3 justify-end">
            <Button
              variant="outline"
              onClick={handleClosePasswordModal}
              disabled={operationLoading}
            >
              取消
            </Button>
            <Button
              onClick={handleUpdatePassword}
              loading={operationLoading}
              disabled={operationLoading}
            >
              更新密码
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default SecuritySection;
