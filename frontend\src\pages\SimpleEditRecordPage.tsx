/**
 * 简化的编辑记录页面
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

interface RecordForm {
  name: string;
  amount: string;
  type: 'income' | 'expense';
  description: string;
  category: string;
}

interface Record {
  id: number;
  name: string;
  amount: number;
  type: 'income' | 'expense';
  description: string;
  category: string;
  createdAt: string;
}

const SimpleEditRecordPage: React.FC = () => {
  const navigate = useNavigate();
  const { bookId, recordId } = useParams<{ bookId: string; recordId: string }>();
  const accountBookId = parseInt(bookId || '0');
  const recordIdNum = parseInt(recordId || '0');

  const [form, setForm] = useState<RecordForm>({
    name: '',
    amount: '',
    type: 'expense',
    description: '',
    category: '其他',
  });

  const [loading, setLoading] = useState(false);
  const [loadingRecord, setLoadingRecord] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载记录数据
  useEffect(() => {
    const fetchRecord = async () => {
      try {
        const response = await fetch(`http://localhost:3001/api/records/${accountBookId}/${recordIdNum}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        });
        const data = await response.json();

        if (data.success) {
          const record: Record = data.data;
          setForm({
            name: record.name,
            amount: record.amount.toString(),
            type: record.type,
            description: record.description || '',
            category: record.category || '其他',
          });
        } else {
          setError(data.message || '获取记录失败');
        }
      } catch (err) {
        setError('网络错误');
        console.error('获取记录失败:', err);
      } finally {
        setLoadingRecord(false);
      }
    };

    if (recordIdNum && accountBookId) {
      fetchRecord();
    }
  }, [recordIdNum, accountBookId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setForm(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!form.name || !form.amount) {
      setError('请填写记录名称和金额');
      return;
    }

    if (isNaN(parseFloat(form.amount)) || parseFloat(form.amount) <= 0) {
      setError('请输入有效的金额');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`http://localhost:3001/api/records/${accountBookId}/${recordIdNum}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          date: new Date().toISOString().split('T')[0], // 添加必需的日期字段
          name: form.name,
          amount: parseFloat(form.amount),
          monthlyAmount: parseFloat(form.amount), // 添加必需的月度金额字段
          renewalTime: '一个月', // 添加默认续期时间
          renewalAmount: 0, // 添加默认续期金额
          remark: form.description,
          isDecreasing: false, // 添加默认递减标志
        }),
      });

      const data = await response.json();

      if (data.success) {
        alert('记录更新成功！');
        navigate(`/account-books/${accountBookId}/records`);
      } else {
        setError(data.message || '更新记录失败');
      }
    } catch (err) {
      setError('网络错误，请重试');
      console.error('更新记录失败:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(`/account-books/${accountBookId}/records`);
  };

  if (loadingRecord) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载记录中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="flex items-center">
            <button 
              onClick={handleCancel}
              className="mr-4 px-3 py-1 text-gray-600 hover:text-gray-800"
            >
              ← 返回
            </button>
            <h1 className="text-2xl font-bold text-gray-900">编辑记录</h1>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-2xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* 记录名称 */}
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                    记录名称 *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={form.name}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入记录名称"
                    required
                  />
                </div>

                {/* 记录类型 */}
                <div>
                  <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                    记录类型 *
                  </label>
                  <select
                    id="type"
                    name="type"
                    value={form.type}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="expense">支出</option>
                    <option value="income">收入</option>
                  </select>
                </div>

                {/* 金额 */}
                <div>
                  <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
                    金额 *
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 sm:text-sm">¥</span>
                    </div>
                    <input
                      type="number"
                      id="amount"
                      name="amount"
                      value={form.amount}
                      onChange={handleInputChange}
                      step="0.01"
                      min="0"
                      className="block w-full pl-7 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="0.00"
                      required
                    />
                  </div>
                </div>

                {/* 分类 */}
                <div>
                  <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                    分类
                  </label>
                  <select
                    id="category"
                    name="category"
                    value={form.category}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="其他">其他</option>
                    <option value="餐饮">餐饮</option>
                    <option value="交通">交通</option>
                    <option value="购物">购物</option>
                    <option value="娱乐">娱乐</option>
                    <option value="医疗">医疗</option>
                    <option value="教育">教育</option>
                    <option value="工资">工资</option>
                    <option value="投资">投资</option>
                  </select>
                </div>

                {/* 描述 */}
                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                    描述
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    value={form.description}
                    onChange={handleInputChange}
                    rows={3}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入记录描述（可选）"
                  />
                </div>

                {/* 错误提示 */}
                {error && (
                  <div className="bg-red-50 border border-red-200 rounded-md p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <div className="text-red-400 text-xl">❌</div>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-red-700">{error}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* 按钮 */}
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className={`px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
                      loading
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                    }`}
                  >
                    {loading ? '更新中...' : '更新记录'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default SimpleEditRecordPage;
